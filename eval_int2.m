function [SR2,tssp,ysp]=eval_int2(pw1,pw2,Ts,T1,Tsim1,Vdd,Vss,Cc,Rc,W0,W1,W3,W21,W22,W23,W24,W30,W31,W33, W40,Ws,Wsp,<PERSON>s,L,L1,L6,<PERSON><PERSON><PERSON>,Cs1,Cs2,Ci1,Ci2,Co)
global pathsp                                        %W0, W1, W3, W21, W22, W23, W24, W30, W31, W33, W40,L,L1,L6,<PERSON><PERSON>,Ibias,Vdd,Vss,CL,Rc
%IB1=IB;
%IB2=IB;
global u0 G0 ovsht h T1
    fp=fopen('INT.sp','w+');
    fprintf(fp,'%s\n','*INTEGRATOR');
    fprintf(fp,'%s\n','.OPTIONs GMINDC=1E-9 post=2 measfall=0 ingold=2 NOMOD accurate numdgt=8 measdgt=8'); 

    fprintf(fp,'%s\n','.subckt two_stage_opamp_CTCMFB AVDD AVSS VCM VINNN VINNP VOUTN VOUTP ');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M0 (net15 VB AVSS AVSS)      nch  w=',W0,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M1 (net12 VINNP net15 net15) nch  w=',W1,'U ' ,'L=',L1,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M2 (net20 VINNN net15 net15) nch  w=',W1,'U ' ,'L=',L1,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M3 (net12 VBIAS AVDD AVDD)   pch  w=',W3,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M4 (net20 VBIAS AVDD AVDD)   pch  w=',W3,'U ' ,'L=',L,'U');

    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M11 (net11 net20 AVDD AVDD)  pch  w=',W21,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M12 (VOUTP net12 AVDD AVDD)  pch  w=',W22,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M13 (net11 net11 AVSS AVSS)  nch  w=',W23,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M14 (VOUTP net11 AVSS AVSS)  nch  w=',W24,'U ' ,'L=',L6,'U');

    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M21 (net19 net12 AVDD AVDD)  pch  w=',W21,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M22 (VOUTN net20 AVDD AVDD)  pch  w=',W22,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M23 (net19 net19 AVSS AVSS)  nch  w=',W23,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M24 (VOUTN net19 AVSS AVSS)  nch  w=',W24,'U ' ,'L=',L6,'U');

    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M30 (net27 VB AVSS AVSS)      nch  w=',W30,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M31 (VBIAS net24 net27 net27) nch  w=',W31,'U ' ,'L=',L1,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M32 (net038 VCM net27 net27)  nch  w=',W31,'U ' ,'L=',L1,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M33 (VBIAS VBIAS AVDD AVDD)   pch  w=',W33,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M34 (net038 net038 AVDD AVDD)   pch  w=',W33,'U ' ,'L=',L,'U');

    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M40 (VB VB AVSS AVSS)         nch  w=',W40,'U ' ,'L=',L,'U');

    fprintf(fp,'%s\n','    R31 (VOUTN net24)    100K');
    fprintf(fp,'%s\n','    R32 (VOUTP net24)    100K');
    fprintf(fp,'%s\n','    C32 (VOUTP net24)    260f');
    fprintf(fp,'%s\n','    C31 (VOUTN net24)    260f');
    fprintf(fp,'%s%9.5f%s\n','    C11 (net12 net50)  ',Cc,'P');
    fprintf(fp,'%s%9.5f%s\n','    C21 (net20 net51)  ',Cc,'P');
    fprintf(fp,'%s%9.5f%s\n','    R11 (net50 VOUTP)   ',Rc/1e3,'K');
    fprintf(fp,'%s%9.5f%s\n','    R21 (net51 VOUTN)   ',Rc/1e3,'K');
    fprintf(fp,'%s%9.5f%s\n','    IBIAS (AVDD VB)     ',Ibias*1e6,'U');   


    fprintf(fp,'%s\n','.ENDS');
    fprintf(fp,'%s\n','.subckt int1 AVDD AVSS Vcm  vincm dac+ dac- p1 p2  vin vip voutn voutp');
    fprintf(fp,'%s\n','X7 (AVDD AVSS Vcm net7 net8 voutn voutp) two_stage_opamp_CTCMFB');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms1 net3 p1 vip AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp1 net3 p2 vip AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms2 net6 p1 Vincm AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp2 net6 p2 Vincm AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms3 Vincm p1 net4 AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp3 Vincm p2 net4 AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms4 vin p1 net18 AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp4 vin p2 net18 AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms5 net8 p2 net6 AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp5 net8 p1 net6 AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms6 dac+ p2 net3 AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp6 dac+ p1 net3 AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms7 net18 p2 dac- AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp7 net18 p1 dac- AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms8 net4 p2 net7 AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp8 net4 p1 net7 AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U m=10');
    fprintf(fp,'%s%9.5f%s\n','Cs1 net3 net6 ',Cs1,'P');
    fprintf(fp,'%s%9.5f%s\n','Cs2 net18 net4 ',Cs2,'P');
    fprintf(fp,'%s%9.5f%s\n','Ci1 net8 voutn ',Ci1,'P');
    fprintf(fp,'%s%9.5f%s\n','Ci2 net7 voutp ',Ci2,'P');
    fprintf(fp,'%s\n','.ENDS');
    fprintf(fp,'%s\n','X6 AVDD AVSS Vcm vincm  dac+ dac- p1  p2  vin vip voutn voutp int1');
    fprintf(fp,'%s%9.5f\n','VDD (AVDD 0) DC  ', Vdd);
    fprintf(fp,'%s%9.5f\n','VSS (AVSS 0) DC ', Vss);
    fprintf(fp,'%s\n','V31 (Vcm 0) 0.9 ');
    fprintf(fp,'%s\n','V32 (Vincm 0) 0.9 ');
    %1n是不交叠时钟
    fprintf(fp,'%s%1.1f%s%1.1f%s\n','Vclk1  p1  0  dc 0 pulse(0 1.8V 0 0.1n 0.1n ', pw1, 'n ' ,Ts, 'n )');
%     fprintf(fp,'%s%1.1f%s%1.1f%s\n','Vclk1-  p1-  0  dc 0 pulse(1.8 0 0 0n 0n ' , pw1, 'n ' ,Ts, 'n )');
    fprintf(fp,'%s%1.1f%s%1.1f%s\n','Vclk2  p2  0  dc 0 pulse(1.8V 0 0 0.1n 0.1n ', pw2,'n ' ,Ts,'n )');
%     fprintf(fp,'%s%1.1f%s%1.1f%s\n','Vclk2-  p2-  0  dc 0 pulse(0 1.8v 0 0n 0n ', pw2,'n ' ,Ts,'n )');
    fprintf(fp,'%s\n','vdac+  dac+ 0  0.9');
    fprintf(fp,'%s\n','vdac-  dac- 0  0.9');
    fprintf(fp,'%s%9.5f%s\n','CL1 Voutn 0 ',Co,'P');
    fprintf(fp,'%s%9.5f%s\n','CL2 Voutp 0 ',Co,'P');
    pw=T1/2-0.2; 
    fprintf(fp,'%s%10.3f%s%10.3f%s%10.3f%s\n','Vvip  vip  0   pulse(0.9 ', 0.9+u0/2 ,' 0 0.1n 0.1n ', pw , 'n ' ,T1, 'n )');
    fprintf(fp,'%s%10.3f%s%10.3f%s%10.3f%s\n','Vvin  vin  0   pulse(0.9 ', 0.9-u0/2 ,' 0 0.1n 0.1n ', pw , 'n ' ,T1, 'n )');
    fprintf(fp,'%s\n','.OP');
    % fprintf(fp,'%s%10.4f%s%10.3f%s\n','.TRAN ', h/1e-9,'n' ,Tsim1,'u' );
    fprintf(fp,'%s%10.4f%s%10.3f%s\n','.TRAN ', h/1e-9,'n' ,Tsim1,'u' );
    fprintf(fp,'%s\n','.MEAS TRAN VMAX MAX V(Voutn,Voutp) FROM=50n TO=100n');
    fprintf(fp,'%s\n','.MEAS TRAN VMIN MIN V(Voutn,Voutp) FROM=50n TO=100n');
    fprintf(fp,'%s\n','.MEAS TRAN TRISE TRIG V(Voutn,Voutp) VAL=''VMIN+0.45*(VMAX-VMIN)'' RISE=1' );
    fprintf(fp,'%s\n','+ TARG V(Voutn,Voutp) VAL=''VMIN+0.5*(VMAX-VMIN)'' RISE=1');
    fprintf(fp,'%s\n','.MEAS SR PARAM=''(VMAX-VMIN)*0.05/TRISE'' ');
    fprintf(fp,'%s\n','.print tran v(voutn,voutp)');
    fprintf(fp,'%s\n','.lib ''rf018.l'' TT');
    fprintf(fp,'%s\n','.END');
    fclose(fp);
    % pathsp1=strcat(pathsp,' -mt 8 -hpp -i INT.sp -o INT -b');
    pathsp1=strcat(pathsp,'  -i INT.sp -o INT -b -mt 8');
    system(pathsp1);   
    fp1=fopen('INT.mt0','r+');
	for m=1:4
        tline=fgets(fp1);
    end
    mt=fscanf(fp1,'%lf',4);
    fclose(fp1);
    SR=mt(4)*1.0E-6;   
    SR2=SR;
    fp1=fopen('INT.lis','r+'); 
    nt=0; NT=Tsim1*1e-6/h;
    while~feof(fp1)
        tline=fgets(fp1);
          if(strncmp(tline,'x',1)==1&&nt==0)
             for j=1:4 
                 tline=fgets(fp1);
                 stmp=fscanf(fp1,'%e%e',[1 2]);
             end
             for k=1
                 ysp(k,1:2) = [0 0 ];
             end
             for k =2:NT   %Tsim1*1000000 
                
                tmp=fscanf(fp1,'%e%e',[1 2]);
                ysp(k,1:2) = tmp;
                
            end

        end
    end
    fclose(fp1);
    clear fp1; % 清除文件指针，确保使用正确的文件指针名字
    its1=max(find(ysp(:,2)<u0*G0*(1-ovsht)));
    its2=max(find(ysp(:,2)>u0*G0*(1+ovsht)));
    if(isempty(its1)==0)
      if(isempty(its2))
        tssp=ysp(its1,1);
      else
        tssp=max(ysp(its1,1),ysp(its2,1));;
      end
    else
      tssp=ysp(end,1);
    end
