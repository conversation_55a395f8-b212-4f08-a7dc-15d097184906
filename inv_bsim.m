%
% Solve W from (Ids,Vgs,Vds,Vbs,L) with BSIM model equ.
%
function [W1]=inv_bsim(ids,vgs,vds,vbs,L,PN)
global Lb Ub gmid
Lb=0.18;Ub=200;
    if (PN>0)
        gmidx=interp1(gmid(:,1), gmid(:,4), -vgs);
        ids0=interp1(gmid(:,4),gmid(:,5),gmidx);
    else
        gmidx=interp1(gmid(:,1), gmid(:,2), vgs);
        ids0=interp1(gmid(:,2),gmid(:,3),gmidx);
    end
    W0=4*abs(ids)/ids0;
    Wdel=1000; k=0; 
while (abs(Wdel)>1e-2*W0)
    k=k+1;
    W0=min(W0,0.95*Ub);
    W0=max(W0,1.5*Lb);
    [ids0 gm0 gds0]=eval_bsim(vgs,vds,vbs,W0,L,PN); 
    [ids1 gm1 gds1]=eval_bsim(vgs,vds,vbs,W0+0.01*W0,L,PN);
    didw=(ids1-ids0)/(0.01*W0);
    W1=W0+(ids-ids0)/didw;
    Wdel=W1-W0;
    W0=W1;
    if (k>20) break; 
    else continue
    end
end


