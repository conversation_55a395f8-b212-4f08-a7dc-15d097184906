****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******
Input File: INT.sp
lic:
lic: FLEXlm: SDK_12.3
lic: USER:   Lenovo               HOSTNAME: DESKTOP-29PD6HE
lic: HOSTID: "00d49eac9d97"       PID:      1792
lic: Using FLEXlm license file:
lic: 27000@DESKTOP-29PD6HE
lic: Checkout 1 hspice
lic: License/Maintenance for hspice will expire on 30-dec-2030/2030.12
lic: 1(in_use)/99(total) FLOATING license(s) on SERVER 27000@DESKTOP-29PD6HE
lic:

 init: begin read circuit files, cpu clock= 1.16E-01
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/behave/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/AD/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/XILINX/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/TLINE/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/TI/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/SIGNET/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/PCI/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/LIN_TEC
                         H/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/PET/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/DI0/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/COMLINE
                         /
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/BURR_BR
                         M/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/BJT/
       option gmindc =    1.000E-09
       option post =     2.00
       option measfall =     0.00
       option ingold =     2.00
       option nomod
       option accurate
       option numdgt =     8.00
       option measdgt =     8.00
 init: end read circuit files, cpu clock= 1.22E-01 peak memory=      62 mb
 init: begin check errors, cpu clock= 1.22E-01
 init: end check errors, cpu clock= 1.29E-01 peak memory=      63 mb
 init: begin setup matrix, pivot=     0 cpu clock= 1.29E-01
       establish matrix -- done, cpu clock= 1.30E-01 peak memory=      63 mb
       re-order matrix -- done, cpu clock= 1.30E-01 peak memory=      63 mb
 init: end setup matrix, cpu clock= 1.32E-01 peak memory=      63 mb
 dcop: begin dcop, cpu clock= 1.32E-01
 dcop: end dcop, cpu clock= 1.49E-01 peak memory=      63 mb tot_iter=     242
 dcop: begin dcop, cpu clock= 1.50E-01
 dcop: end dcop, cpu clock= 1.64E-01 peak memory=      63 mb tot_iter=     439
 output: INT.mt0
 sweep: tran tran0    begin, stop_t=  1.00E-07 #sweeps=**** cpu clock= 1.65E-01
 tran: time= 1.3471E-08 tot_iter=     109 conv_iter=      43 cpu clock= 1.72E-01
 tran: time= 2.3471E-08 tot_iter=     113 conv_iter=      45 cpu clock= 1.73E-01
 tran: time= 3.3471E-08 tot_iter=     117 conv_iter=      47 cpu clock= 1.73E-01
 tran: time= 4.3471E-08 tot_iter=     121 conv_iter=      49 cpu clock= 1.73E-01
 tran: time= 5.0000E-08 tot_iter=     161 conv_iter=      64 cpu clock= 1.76E-01
 tran: time= 6.0382E-08 tot_iter=     243 conv_iter=      96 cpu clock= 4.38E-01
 tran: time= 7.0814E-08 tot_iter=     251 conv_iter=     100 cpu clock= 4.39E-01
 tran: time= 8.0718E-08 tot_iter=     255 conv_iter=     102 cpu clock= 4.39E-01
 tran: time= 9.0718E-08 tot_iter=     259 conv_iter=     104 cpu clock= 4.39E-01
 tran: time= 1.0000E-07 tot_iter=     263 conv_iter=     106 cpu clock= 4.40E-01
 sweep: tran tran0    end, cpu clock= 4.40E-01 peak memory=      65 mb
>info:         ***** hspice job concluded
 lic: Release hspice token(s)
