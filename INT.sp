*INTEGRATOR
.OPTIONs GMINDC=1E-9 post=2 measfall=0 ingold=2 NOMOD accurate numdgt=8 measdgt=8
.subckt two_stage_opamp_CTCMFB AVDD AVSS VCM VINNN VINNP VOUTN VOUTP 
    M0 (net15 VB AVSS AVSS)      nch  w=  1.77319U L=  0.30000U
    M1 (net12 VINNP net15 net15) nch  w=  4.97029U L=  0.30000U
    M2 (net20 VINNN net15 net15) nch  w=  4.97029U L=  0.30000U
    M3 (net12 VBIAS AVDD AVDD)   pch  w=  3.14792U L=  0.30000U
    M4 (net20 VBIAS AVDD AVDD)   pch  w=  3.14792U L=  0.30000U
    M11 (net11 net20 AVDD AVDD)  pch  w=153.70371U L=  0.30000U
    M12 (VOUTP net12 AVDD AVDD)  pch  w=161.17705U L=  0.30000U
    M13 (net11 net11 AVSS AVSS)  nch  w= 36.48180U L=  0.30000U
    M14 (VOUTP net11 AVSS AVSS)  nch  w= 34.30705U L=  0.30000U
    M21 (net19 net12 AVDD AVDD)  pch  w=153.70371U L=  0.30000U
    M22 (VOUTN net20 AVDD AVDD)  pch  w=161.17705U L=  0.30000U
    M23 (net19 net19 AVSS AVSS)  nch  w= 36.48180U L=  0.30000U
    M24 (VOUTN net19 AVSS AVSS)  nch  w= 34.30705U L=  0.30000U
    M30 (net27 VB AVSS AVSS)      nch  w=  1.77319U L=  0.30000U
    M31 (VBIAS net24 net27 net27) nch  w=  5.19977U L=  0.30000U
    M32 (net038 VCM net27 net27)  nch  w=  5.19977U L=  0.30000U
    M33 (VBIAS VBIAS AVDD AVDD)   pch  w=  3.08502U L=  0.30000U
    M34 (net038 net038 AVDD AVDD)   pch  w=  3.08502U L=  0.30000U
    M40 (VB VB AVSS AVSS)         nch  w=  1.68016U L=  0.30000U
    R31 (VOUTN net24)    100K
    R32 (VOUTP net24)    100K
    C32 (VOUTP net24)    260f
    C31 (VOUTN net24)    260f
    C11 (net12 net50)    0.22706P
    C21 (net20 net51)    0.22706P
    R11 (net50 VOUTP)     1.76648K
    R21 (net51 VOUTN)     1.76648K
    IBIAS (AVDD VB)      41.78201U
.ENDS
.subckt int1 AVDD AVSS Vcm  vincm dac+ dac- p1 p2  vin vip voutn voutp
X7 (AVDD AVSS Vcm net7 net8 voutn voutp) two_stage_opamp_CTCMFB
Ms1 net3 p1 vip AVSS NCH W=  2.00000U L=  0.18000U m=10
Msp1 net3 p2 vip AVDD PCH W=  6.00000U L=  0.18000U m=10
Ms2 net6 p1 Vincm AVSS NCH W=  2.00000U L=  0.18000U m=10
Msp2 net6 p2 Vincm AVDD PCH W=  6.00000U L=  0.18000U m=10
Ms3 Vincm p1 net4 AVSS NCH W=  2.00000U L=  0.18000U m=10
Msp3 Vincm p2 net4 AVDD PCH W=  6.00000U L=  0.18000U m=10
Ms4 vin p1 net18 AVSS NCH W=  2.00000U L=  0.18000U m=10
Msp4 vin p2 net18 AVDD PCH W=  6.00000U L=  0.18000U m=10
Ms5 net8 p2 net6 AVSS NCH W=  2.00000U L=  0.18000U m=10
Msp5 net8 p1 net6 AVDD PCH W=  6.00000U L=  0.18000U m=10
Ms6 dac+ p2 net3 AVSS NCH W=  2.00000U L=  0.18000U m=10
Msp6 dac+ p1 net3 AVDD PCH W=  6.00000U L=  0.18000U m=10
Ms7 net18 p2 dac- AVSS NCH W=  2.00000U L=  0.18000U m=10
Msp7 net18 p1 dac- AVDD PCH W=  6.00000U L=  0.18000U m=10
Ms8 net4 p2 net7 AVSS NCH W=  2.00000U L=  0.18000U m=10
Msp8 net4 p1 net7 AVDD PCH W=  6.00000U L=  0.18000U m=10
Cs1 net3 net6   1.21200P
Cs2 net18 net4   1.21200P
Ci1 net8 voutn   1.08400P
Ci2 net7 voutp   1.08400P
.ENDS
X6 AVDD AVSS Vcm vincm  dac+ dac- p1  p2  vin vip voutn voutp int1
VDD (AVDD 0) DC    1.80000
VSS (AVSS 0) DC   0.00000
V31 (Vcm 0) 0.9 
V32 (Vincm 0) 0.9 
Vclk1  p1  0  dc 0 pulse(0 1.8V 0 0.1n 0.1n 49.8n 100.0n )
Vclk2  p2  0  dc 0 pulse(1.8V 0 0 0.1n 0.1n 49.8n 100.0n )
vdac+  dac+ 0  0.9
vdac-  dac- 0  0.9
CL1 Voutn 0   1.49200P
CL2 Voutp 0   1.49200P
Vvip  vip  0   pulse(0.9      1.079 0 0.1n 0.1n     49.800n    100.000n )
Vvin  vin  0   pulse(0.9      0.721 0 0.1n 0.1n     49.800n    100.000n )
.OP
.TRAN     0.0100n     0.100u
.MEAS TRAN VMAX MAX V(Voutn,Voutp) FROM=50n TO=100n
.MEAS TRAN VMIN MIN V(Voutn,Voutp) FROM=50n TO=100n
.MEAS TRAN TRISE TRIG V(Voutn,Voutp) VAL='VMIN+0.45*(VMAX-VMIN)' RISE=1
+ TARG V(Voutn,Voutp) VAL='VMIN+0.5*(VMAX-VMIN)' RISE=1
.MEAS SR PARAM='(VMAX-VMIN)*0.05/TRISE' 
.print tran v(voutn,voutp)
.lib 'rf018.l' TT
.END
