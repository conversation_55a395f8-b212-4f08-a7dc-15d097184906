function [ovsht_model tslg y ylin]=model(K,UGB,SR)
% function [f tslg y ]=model(K,UGB)
% %积分器的阶跃响应优化模型（二阶）
global G0 beta a t u0  ovsht Vov1

%   UGB = UGB*0.4;
    h=t(2)-t(1);
    GBW=beta*UGB/(2*pi);%闭环带宽
%   T = GBW*t;
    zeta=0.5*sqrt(K/beta);
    omegan=4*pi*GBW*zeta;
%   phi = atan(sqrt((4-K)/K));
    p1=omegan*(zeta+sqrt(zeta*zeta-1));
    p2=omegan*(zeta-sqrt(zeta*zeta-1));
    if(zeta<1)     
        ylin=G0*u0*(1-a*((1/sqrt(1-zeta*zeta))*exp(-zeta*omegan*t).*sin(omegan*sqrt(1-zeta*zeta)*t+atan(sqrt(1-zeta*zeta)/zeta))));
    elseif(zeta==1)      
        ylin=G0*u0*(1-a*(1+p1*t).*exp(-p1*t));
    else 
        ylin=G0*u0*(1-a*((p2/(p2-p1))*exp(-p1*t)+(p1/(p1-p2))*exp(-p2*t)));  
    end
%     epsilon=(1/sqrt(1-zeta*zeta))*exp(-zeta*omegan*t).*sin(omegan*sqrt(1-zeta*zeta)*t+atan(sqrt(1-zeta*zeta)/zeta));
%     epsilon=sqrt(4/(4-K))*exp(-pi*K*T).*sin(sqrt(K-(K*K/4))*2*pi*T+phi);
%     ylin=G0*u0*(1-a*epsilon);
% if(ysp~=1)
% figure; plot(t,[ylin' ysp']); grid; legend('model','simulation');
% end
    dylin=diff(ylin);
    [dylinmax nmax] = max(dylin);
    h=t(2)-t(1);
    tmax = nmax *h;
    n1=1;
    while 1
       n1=n1+1;
       if(dylin(n1-1)>=SR*h)
           break;
       end
    end
    VSS=SR*u0*h/(max(dylin));
    yss=(VSS/u0)*ylin;
    dyss=diff(yss);
    [ms,nm] = max(dyss);
    t1=n1*h;
    tm=nm*h;  

 if(zeta<1)
       
       for j=1:1:n1  
            y1(j)=G0*u0*(1-a*((1/sqrt(1-zeta*zeta))*exp(-zeta*omegan*t(j)).*sin(omegan*sqrt(1-zeta*zeta)*t(j)+atan(sqrt(1-zeta*zeta)/zeta))));       
       end
       yt1=y1(n1);
       ysstm=(VSS/u0)*G0*u0*(1-a*((1/sqrt(1-zeta*zeta))*exp(-zeta*omegan*tm)*sin(omegan*sqrt(1-zeta*zeta)*tm+atan(sqrt(1-zeta*zeta)/zeta))));
       tls=(G0*(u0-VSS)+ysstm-yt1)/SR+t1;
       nls=floor(tls/h)+1;     
       t2 = t(2:nls-n1); 
       y2=SR*t2+yt1; 
       l = length(t(nls:end));
       Tls=t(nm+1:l+nm);
       y3=G0*(u0-VSS)+(VSS/u0)*G0*u0*(1-a*((1/sqrt(1-zeta*zeta))*exp(-zeta*omegan*Tls).*sin(omegan*sqrt(1-zeta*zeta)*Tls+atan(sqrt(1-zeta*zeta)/zeta))));
       
 elseif(zeta==1)
     
        
        for j=1:1:n1           
            y1(j)=G0*u0*(1-a*((1+p1*t(j)).*exp(-p1*t(j))));       
        end
       
       yt1=y1(n1);
       ysstm=(VSS/u0)*G0*u0*(1-a*((1+p1*tm)*exp(-p1*tm)));
       tls=(G0*(u0-VSS)+ysstm-yt1)/SR+t1;
       nls=floor(tls/h)+1;
%        
       t2 = t(2:nls-n1); 
       y2=SR*t2+yt1;
           
       l = length(t(nls:end));
       Tls=t(nm+1:l+nm);  
       y3=G0*(u0-VSS)+(VSS/u0)*G0*u0*(1-a*((1+p1*Tls).*exp(-p1*Tls)));
       
 else
       
       for j=1:1:n1    
            
            y1(j)=G0*u0*(1-a*((p2/(p2-p1))*exp(-p1*t(j))+(p1/(p1-p2))*exp(-p2*t(j))));
            
       end
       
       yt1=y1(n1);
       ysstm=(VSS/u0)*G0*u0*(1-a*((p2/(p2-p1))*exp(-p1*tm)+(p1/(p1-p2))*exp(-p2*tm)));
       tls=(G0*(u0-VSS)+ysstm-yt1)/SR+t1;
       nls=floor(tls/h)+1;
%        
       t2 = t(2:nls-n1); 
       y2=SR*t2+yt1;
           

        
       l = length(t(nls:end));
       Tls=t(nm+1:l+nm);  
       y3=G0*(u0-VSS)+(VSS/u0)*G0*u0*(1-a*((p2/(p2-p1))*exp(-p1*Tls)+(p1/(p1-p2))*exp(-p2*Tls)));
 end
          
    y=[y1,y2,y3];

        for k=1:size(t,2)
            if (y(k)>u0*G0*(1-ovsht))
                tslg=t(k-1)+h*(u0*G0*(1-ovsht)-y(k-1))/(y(k)-y(k-1));
                break;
            end
        end
   ovsht_model=(max(y)-u0*G0)/(u0*G0);
end