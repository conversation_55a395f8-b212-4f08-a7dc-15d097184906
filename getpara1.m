function [ids,vgs,vds,vdsat,gm,gds]=getpara1

    fp1=fopen('INT.lis','r+');  
    ns=0; flagpz=0;
    while~feof(fp1)
        tline=fgets(fp1);
        if(strncmp(tline,' region',7)==1)
           
            tmps=fscanf(fp1,'%s',[1 1]); tmp=fscanf(fp1,'%e%e%e%e',[1 4]);
             len_tmp = length(tmp);
            ids1(4*ns+1:4*ns+len_tmp)=tmp;  
            for i=1:3 tline=fgets(fp1); end
        %if(strncmp(tline,'  ibd',5)==1&&ns==0)
            tmps=fscanf(fp1,'%s',[1 1]); tmp=fscanf(fp1,'%e%e%e%e%e%e',[1 4]);
            vgs1(4*ns+1:4*ns+len_tmp)=tmp;
            tmps=fscanf(fp1,'%s',[1 1]); tmp=fscanf(fp1,'%e%e%e%e%e%e',[1 4]);
            vds1(4*ns+1:4*ns+len_tmp)=tmp;
        end
        if(strncmp(tline,'  vth',5)==1)
            tmps=fscanf(fp1,'%s',[1 1]); tmp=fscanf(fp1,'%e%e%e%e%e%e',[1 4]);
            vdsat1(4*ns+1:4*ns+len_tmp)=tmp;
         %end
         %if(strncmp(tline,'  gam eff',9)==1&&ns==0)
           for i=1:4 tline=fgets(fp1); end
            stmp=fscanf(fp1,'%s',[1,1]);
            tmp=fscanf(fp1,'%e%e%e%e%e%e',[1 4]);
            gm1(4*ns+1:4*ns+len_tmp)=tmp;
            stmp=fscanf(fp1,'%s',[1,1]);
            tmp=fscanf(fp1,'%e%e%e%e%e%e',[1 4]);
            gds1(4*ns+1:4*ns+len_tmp)=tmp;
            stmp=fscanf(fp1,'%s',[1,1]); tmp=fscanf(fp1,'%e%e%e%e%e%e',[1 4]);
            gmb1(4*ns+1:4*ns+len_tmp)=tmp;
            tline=fgets(fp1);
            ns=ns+1;
            
        end
           %  if(strncmp(tline,' element      2:m12          2:m13          2:m14',49)==1)
           %      for i=1:2 tline=fgets(fp1); end
           %      tmps=fscanf(fp1,'%s',[1 1]); tmp=fscanf(fp1,'%e%e%e%e',[1 3]);
           %      ids2(1:3)=tmp;  
           %      for i=1:3 tline=fgets(fp1); end
           %  %if(strncmp(tline,'  ibd',5)==1&&ns==0)
           %      tmps=fscanf(fp1,'%s',[1 1]); tmp=fscanf(fp1,'%e%e%e%e%e%e',[1 3]);
           %      vgs2(1:3)=tmp;
           %      tmps=fscanf(fp1,'%s',[1 1]); tmp=fscanf(fp1,'%e%e%e%e%e%e',[1 3]);
           %      vds2(1:3)=tmp;
           %      for i=1:3 tline=fgets(fp1); end
           %      tmps=fscanf(fp1,'%s',[1 1]); tmp=fscanf(fp1,'%e%e%e%e%e%e',[1 3]);
           %      vdsat2(1:3)=tmp;
           %      for i=1:4 tline=fgets(fp1); end
           %      stmp=fscanf(fp1,'%s',[1,1]);
           %      tmp=fscanf(fp1,'%e%e%e%e%e%e',[1 3]);
           %      gm2(1:3)=tmp;
           %      stmp=fscanf(fp1,'%s',[1,1]);
           %      tmp=fscanf(fp1,'%e%e%e%e%e%e',[1 3]);
           %      gds2(1:3)=tmp;
           %      stmp=fscanf(fp1,'%s',[1,1]); tmp=fscanf(fp1,'%e%e%e%e%e%e',[1 3]);
           %      gmb2(1:3)=tmp;
           %      tline=fgets(fp1);         
           % end
         
    end

fclose(fp1);
   % 
   % ids = [ids1 ids2];vgs = [vgs1,vgs2]; vds = [vds1,vds2];
   % vdsat = [vdsat1 vdsat2]; gm = [gm1 gm2]; gds = [gds1 gds2];
   
   ids=ids1;vgs=vgs1;vds=vds1;vdsat=vdsat1;gm=gm1;gds=gds1;

   
 end
