% try
clc
clear 
close all
dbstop if error
system('taskkill /IM hspice.exe /F');  % 关闭 HSPICE 进程
fclose('all');                         % 关闭所有打开的文件句柄

% 指定要保留的文件名列表
keepFiles = {
    'eval_bsim.m', 'eval_int.m', 'eval_int1.m', 'eval_int1.m', 'eval_int_ns.m', ...
    'eval_opamp.m', 'fovsht1.m', 'fovshtp.m', 'fovshtp1.m', ...
    'fsettle1.m', 'fsettlep.m', 'fsettlep1.m', 'getpara1.m', ...
    'gm_id.txt', 'handle_ysp1.m', 'int_ts.m', 'int_ts1.m', ...
    'int_ts2.m', 'inv_bsim.m', 'inv_bsim1.m', 'model.m', ...
    'ms018_v1p7.lib', 'ms018_v1p7.mdl', 'op2sizing.m', 'rf018.l', ...
    'untitled.m','untitled2.m','untitled3.m','SwitchQ.m','int_ts3.m','New Folder','New Folder1','switch.png','opampwrite.m',
};

% 获取当前文件夹中的所有文件
files = dir;

% 遍历所有文件
for k = 1:length(files)
    % 获取文件名
    [~, name, ext] = fileparts(files(k).name);
    filename = [name, ext];
    
    % 如果文件不是指定保留的文件并且不是目录，则删除
    if ~ismember(filename, keepFiles) && ~files(k).isdir
        delete(files(k).name);
        disp(['Deleted: ', files(k).name]);
    end
end
global pathsp moslib Lb Ub gmid t Vov1 u0 KS UGBS ydel
global L Vdd Vss Cc CL Co Av ts ovsht  Vinmax Vinmin vsd6 vds7 VTn Cox
global G0 beta a Ws Wsp Ls kite ysim icompen Itot
global fs Ts pw1 pw2 fin1 T1 Tsim1 h amp1 
pathsp='C:\Synopsys\Hspice_P-2019.06-SP1-1\WIN64\hspice.com -mt 16 ';
kBOLTSMAN=1.381e-23; Temp=273+25;epislon=3.9*8.854e-12; 
un=275.5555875e-4; up=116.6094811e-4; 
toxn=4.1e-9; toxp=toxn;  %0.18um
UnCox=1e6*un*epislon/toxn; UpCox=1e6*up*epislon/toxp;
Cox=epislon/toxp;
VTn=0.485; VTp=-0.45; VTnmin=VTn; VTpmax=-VTp; % 0.18um  
moslib = '.lib ''rf018.l'' TT';
L=0.3; L1=L; L6=0.3; 



% 电容系数
opx=1;%运放编号 
Cs=1.212; Ci=1.084; Co=1.492; CIN=0.12;  Ci3=0.0;
% Cs=1.492; Ci=1; Co=1; CIN=0.06;  Ci3=0.0;

Vdd=1.8; Vss=0;  Lb=0.18; Ub=100;  %180nm
Vout0=0.5*(Vdd+Vss);
Vin1=Vout0; Vin2=Vout0; vbs = 0;
load gm_id.txt -ascii;  gmid=gm_id;
% Performance Specifications
Tend=0.1e-6; h=0.01e-9; t=0:h:Tend;  
Vinmax=1.6; Vinmin=0.7; Av=70;   %0.18um
ovsht=0.5e-2;ovsht=1e-2;%ovsht=2e-2;ovsht=3e-2; %1 peak & Settling time
fclk=80e6;    ts=0.5/fclk;
% t1=ts;ts=0.95*ts;%非交叠时钟时间+冗余时间



Ci=Ci+Ci3;
CL = Co + (Cs+CIN)*Ci/(Cs+CIN+Ci);
CL = Co + Cs*Ci/(Cs+Ci);
beta=Ci/(Cs+CIN+Ci); G0=Cs/Ci;
a=1+Ci*Ci/(Co*(Cs+Ci+CIN)+Ci*(Cs+CIN));
Ci=Ci-Ci3;

voutx=0.4;Vov1=0.12; u0=voutx/G0;Cc=0.18*CL;%可调选项


SR0 = 100e6; UGBS=SR0/(Vov1);
K=fzero(@fovsht1,2);
f1 = fovsht1(K);
KS = K; UGB0 = UGBS; SR0 = UGB0*Vov1;
UGB=fzero(@fsettle1,UGB0);
SR = UGB*Vov1; PM = atan(K)*180/pi; 
[ovsht_model ts_model y ylin]=model(K,UGB,SR);
UGB = UGB*1e-6; SR = SR*1e-6;
Vov3=Vdd-Vinmax-VTpmax+VTnmin;
vsd6=Vdd-Vout0; vds7=Vout0-Vss;
icompen=1; %indicator for zero nulling or pz cancellation compensation schme
gm1=UGB*Cc*1e-6;  
Cs1=Cs; Cs2=Cs1;  Ci1=Ci; Ci2=Ci1;
Ws=2; Wsp=6; Ls=0.18;  %开关尺寸
T1 = 100; pw1 = 49.8; pw2 = 49.8; Ts = 100; Tsim1 = Tend/1e-6;

global  cxx
cxx=0;

[W0,W1,W3,W6,W7,W9,W10,W12,W14,Cc,Ibias,Rc]=op2sizing(gm1,UGB,SR,K);
pf=eval_opamp(W0,W1,W3,W6,W7,W9,W10,W12,W14,L,L1,L6,Cc,Ibias,Vdd,Vss,CL,Rc); 
% cxx=Cc-(gm1/pf(2)*10);
cxx=(1e6)*gm1/(10*pf(2));Cc1=Cc;
[W0,W1,W3,W6,W7,W9,W10,W12,W14,Cc,Ibias,Rc]=op2sizing(gm1,UGB,SR,K);

PM = atan(K)*180/pi; 
[SR2,tssp,ysp]=eval_int1(pw1,pw2,Ts,T1,Tsim1,Vdd,Vss,Cc1,Rc,W0,W1,W3,W6,W7,W9,W10,W12,W14,Ws,Wsp,Ls,L,L1,L6,Ibias,Cs1,Cs2,Ci1,Ci2,Co);
[ovsht_sim,ts_sim,ysim,tsim] = handle_ysp1(ysp); tssp=tssp-5e-8;

figure; plot(t/1e-9,[y' ysim']); grid; legend('model','simulation'); xlabel('Time(ns)');
yline((1+ovsht)*ysim(1,end), '--', 'Color', 'k'); % 在 y=a 处添加黑色虚线
yline((1-ovsht)*ysim(1,end), '--', 'Color', 'k'); % 在 y=b 处添加黑色虚线
yline(ysim(1,end), '--', 'Color', 'r'); % 在 y=b 处添加黑色虚线
xline(ts/1e-9, '--', 'Color', 'k'); % 在 x=c 处添加黑色虚线

vjump=-min(ysim(find(ysim<=0))); a=vjump/(G0*u0)+1;
delovsht=(ovsht_sim-ovsht)/ovsht;  delts=(ts_sim-ts)/ts;
tsk(1)=tssp; tsx(1)=ts_sim; ovshtk(1)=ovsht_sim; UGBk(1)=UGB; Kk(1)=K;
kite=1; hf1=figure;hf2=figure; kend=8; ovsht0=ovsht; tol = 0.005;
while (delts>0||delts<-tol||delovsht>0||delovsht<-tol)  % (abs(delts)>tol||abs(delovsht)>tol) 
    kxx=1;
    UGB0=UGB; ts_sim0=ts_sim;
    if (kite==1) 
        alfa=1.1;
        if (delts<0) alfa=0.9; end
    else
        UGB1=UGB+ru*(ts-ts_sim);
        alfa=UGB1/UGB;
    end
    alfa=min(alfa,1.25); alfa=max(alfa,0.75);
    UGB=alfa*UGB; SR=alfa*SR;
    [W0,W1,W3,W6,W7,W9,W10,W12,W14,Cc,Ibias,Rc]=op2sizing(gm1,UGB,SR,K);
    % pf=eval_opamp(W0,W1,W3,W6,W7,W9,W10,W12,W14,L,L1,L6,Cc,Ibias,Vdd,Vss,CL,Rc*kxx);
    [SR2,tssp,ysp]=eval_int1(pw1,pw2,Ts,T1,Tsim1,Vdd,Vss,Cc1,Rc*kxx,W0,W1,W3,W6,W7,W9,W10,W12,W14,Ws,Wsp,Ls,L,L1,L6,Ibias,Cs1,Cs2,Ci1,Ci2,Co);
    [ovsht_sim,ts_sim,ysim,tsim] = handle_ysp1(ysp);
    ru=(UGB0-UGB)/(ts_sim0-ts_sim); 
    K0=K; ovsht_sim0=ovsht_sim;
    if (kite==1)
        if (ovsht_sim>ovsht) Kdel=0.2;
        else Kdel=-0.1; end
    else
        Kdel=rk*(ovsht-ovsht_sim);
    end
    if (Kdel>0.5) Kdel=0.5; end
    if (Kdel<-0.5) Kdel=-0.5; end
    K1=K+Kdel;
    K1=min(K1,4.7); K1=max(K1,0.5);
    K=K1;
    [W0,W1,W3,W6,W7,W9,W10,W12,W14,Cc,Ibias,Rc]=op2sizing(gm1,UGB,SR,K);
    % pf=eval_opamp(W0,W1,W3,W6,W7,W9,W10,W12,W14,L,L1,L6,Cc,Ibias,Vdd,Vss,CL,Rc);
    [SR2,tssp,ysp]=eval_int1(pw1,pw2,Ts,T1,Tsim1,Vdd,Vss,Cc1,Rc,W0,W1,W3,W6,W7,W9,W10,W12,W14,Ws,Wsp,Ls,L,L1,L6,Ibias,Cs1,Cs2,Ci1,Ci2,Co);
    [ovsht_sim,ts_sim,ysim,tsim] = handle_ysp1(ysp); tssp=tssp-5e-8;
    rk=(K0-K)/(ovsht_sim0-ovsht_sim); 
    Rc1=Rc;ixx=0;
while((abs((ovsht-ovsht_sim)/ovsht)>1e-1)|(ovsht<ovsht_sim))
    ixx=ixx+1;if ixx==8 break;end
    K0=K; ovsht_sim0=ovsht_sim;
        if (ovsht_sim>ovsht) Kdelx=0.05;
        else Kdelx=-0.04; end
    rng('default') % For reproducibility
    mu = 0;
    sigma = 0.005;
    r = random('Normal',mu,sigma);
        Kdelx=Kdelx+r;
    kxx=kxx+Kdelx;
    if (Kdel>0.5) Kdel=0.5; end
    if (Kdel<-0.5) Kdel=-0.5; end
    K1=K+Kdel;
    K1=min(K1,4.7); K1=max(K1,0.5);
    K=K1;
    % [W0,W1,W3,W6,W7,W9,W10,W12,W14,Cc,Ibias,Rc]=op2sizing(gm1,UGB,SR,K);
    % pf=eval_opamp(W0,W1,W3,W6,W7,W9,W10,W12,W14,L,L1,L6,Cc,Ibias,Vdd,Vss,CL,Rc);
    Rc=Rc1*kxx;
    if Rc<0 
        Rc=0;end
    [SR2,tssp,ysp]=eval_int1(pw1,pw2,Ts,T1,Tsim1,Vdd,Vss,Cc1,Rc,W0,W1,W3,W6,W7,W9,W10,W12,W14,Ws,Wsp,Ls,L,L1,L6,Ibias,Cs1,Cs2,Ci1,Ci2,Co);
    [ovsht_sim,ts_sim,ysim,tsim] = handle_ysp1(ysp); tssp=tssp-5e-8;
    rk=(K0-K)/(ovsht_sim0-ovsht_sim); 
end
    delovsht=(ovsht_sim-ovsht)/ovsht;  delts=(ts_sim-ts)/ts;

    [ids,vgs,vds,vdsat,gm,gds]=getpara1;
    ids6=-ids(39);
    vgs1=vgs(34); vsg3=-vgs(36); vgs0=vgs(33); vs1=Vin1-vgs1;
    vds1=vds(34); vds0=vds(33);
    vdsat1=vdsat(34); vdsat0=vdsat(33);

    Vinmax1=Vdd-vsg3+vgs1-vdsat1;
    Vinmin1=Vss+vdsat0+vgs1;

    kite=kite+1;
    ovshtk(kite)=ovsht_sim;figure(hf1); plot(ovshtk); grid;
    tsk(kite)=tssp;        figure(hf2); plot(tsk);    grid;
    if (kite==kend)

% 设置参数  
fs = 44100;  % 采样频率（每秒采样点数）  
duration = 0.1;  % 持续时间（秒）  
frequency = 1000;  % 频率（赫兹）  
% 生成时间向量  
t = 0:1/fs:duration-1/fs;   
% 生成正弦波信号  
beep_signal = sin(2 * pi * frequency * t);  
% 播放声音  
sound(beep_signal, fs);

        R=input('Continue?');
        if (R==0) break;
        else kend=kite+R;
        end
    end
end



% figure;
% plot(ysp(:,1), ysp(:,2), '.', 'MarkerSize', 10);  % 使用点标记显示数据点
% % grid on;plot(t, y, '.', 'MarkerSize', 10);  % 使用点标记显示数据点
% xline(ts , '--', 'Color', 'k'); xlim([0 2.5*ts]);  % 限制横坐标范围为[a, b]
% yline((1 - ovsht) * ysim(1, end), '--', 'Color', 'k'); 
% yline((1 + ovsht) * ysim(1, end), '--', 'Color', 'k'); 
% pf=eval_opamp(W0,W1,W3,W6,W7,W9,W10,W12,W14,L,L1,L6,Cc,Ibias,Vdd,Vss,CL,Rc);
Tend=0.1e-6; h=0.01e-9; t=0:h:Tend;  
figure;
plot(t, ysim, '.', 'MarkerSize', 10);  % 使用点标记显示数据点
% plot(t(1:4410), ysim(1:4410), '.', 'MarkerSize', 10);  % 使用点标记显示数据点
% grid on;plot(t, y, '.', 'MarkerSize', 10);  % 使用点标记显示数据点
% xline(ts , '--', 'Color', 'k'); xlim([0 2.5*ts]);  % 限制横坐标范围为[a, b]
%xline(t1 , '--', 'Color', 'k'); 
xline(ts  , '--', 'Color', 'k'); 
xlim([0 2.5*ts]);  % 限制横坐标范围为[a, b]
yline((1 - ovsht) * ysim(1, end), '--', 'Color', 'k'); 
yline((1 + ovsht) * ysim(1, end), '--', 'Color', 'k'); 


% opampwrite(W0,W1,W3,W6,W7,W9,W10,W12,W14,L,L1,L6,Cc,Ibias,Vdd,Vss,CL,Rc,opx);  
% nameop1 = sprintf('opamp%d', opx); % 生成文件名
% sourceFile = [nameop1, '.sp']; % 拼接文件扩展名
% sourceFolder = 'C:\baidu\work4'; % 源文件所在文件夹
% targetFolder = 'C:\baidu\work4\New Folder1'; % 目标文件夹路径
% % 生成完整的源文件路径
% fullSourceFilePath = fullfile(sourceFolder, sourceFile);
% % 复制文件
% if exist(fullSourceFilePath, 'file') % 检查文件是否存在
%     copyfile(fullSourceFilePath, targetFolder); % 复制文件到目标文件夹
%     disp('文件复制成功！');
% else
%     disp('源文件不存在，无法复制。');
% end

% 设置参数  
fs = 44100;  % 采样频率（每秒采样点数）  
duration = 0.1;  % 持续时间（秒）  
frequency = 1000;  % 频率（赫兹）  
% 生成时间向量  
t = 0:1/fs:duration-1/fs;   
% 生成正弦波信号  
beep_signal = sin(2 * pi * frequency * t);  
% 播放声音  
sound(beep_signal, fs);