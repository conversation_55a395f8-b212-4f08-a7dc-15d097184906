function [ovsht_sim,ts_sim,ysim,tsim] = handle_ysp(ysp)

global u0 G0 ovsht
    l=length(ysp(:,2));
    a1=floor((l+0.5)/2);
    ysim1 = ysp(a1+1:end ,2);
    for i=1:l/2+1

       ysim2(i) = ysim1(end);

    end
    ysim = [ysim1',ysim2];
    tsim = ysp(1:end,1);
    ovsht_sim=(max(ysim)-u0*G0)/(u0*G0);
    ovsht_sim=(max(ysim)-ysp(end,end))/(ysp(end,end));
    h=ysp(2,1)-ysp(1,1);
    % for k=1:size(tsim)
    %     if (ysim(k)>u0*G0*(1-ovsht))
    %         ts_sim=tsim(k-1)+h*(u0*G0*(1-ovsht)-ysim(k-1))/(ysim(k)-ysim(k-1));
    %         break;
    %     end
    % end

    for k=1:size(tsim)
        if (ysim(k)>ysp(end,end)*(1-ovsht))
            ts_sim=tsim(k-1)+h*(ysp(end,end)*(1-ovsht)-ysim(k-1))/(ysim(k)-ysim(k-1));
            break;
        end
    end


end


