****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******
Input File: eval_mos.sp
lic:
lic: FLEXlm: SDK_12.3
lic: USER:   Lenovo               HOSTNAME: DESKTOP-29PD6HE
lic: HOSTID: "00d49eac9d97"       PID:      17652
lic: Using FLEXlm license file:
lic: 27000@DESKTOP-29PD6HE
lic: Checkout 1 hspice
lic: License/Maintenance for hspice will expire on 30-dec-2030/2030.12
lic: 1(in_use)/99(total) FLOATING license(s) on SERVER 27000@DESKTOP-29PD6HE
lic:

 init: begin read circuit files, cpu clock= 1.02E-01
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/behave/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/AD/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/XILINX/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/TLINE/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/TI/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/SIGNET/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/PCI/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/LIN_TEC
                         H/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/PET/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/DI0/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/COMLINE
                         /
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/BURR_BR
                         M/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/BJT/
       option post =     2.00
       option ingold =     2.00
 init: end read circuit files, cpu clock= 1.06E-01 peak memory=      61 mb
 init: begin check errors, cpu clock= 1.06E-01
 init: end check errors, cpu clock= 1.12E-01 peak memory=      62 mb
 init: begin setup matrix, pivot=     0 cpu clock= 1.12E-01
       establish matrix -- done, cpu clock= 1.12E-01 peak memory=      62 mb
       re-order matrix -- done, cpu clock= 1.12E-01 peak memory=      62 mb
 init: end setup matrix, cpu clock= 1.12E-01 peak memory=      62 mb
 dcop: begin dcop, cpu clock= 1.12E-01
 dcop: end dcop, cpu clock= 1.14E-01 peak memory=      62 mb tot_iter=       4
>info:         ***** hspice job concluded
 lic: Release hspice token(s)
