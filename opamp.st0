****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******
Input File: opamp.sp
lic:
lic: FLEXlm: SDK_12.3
lic: USER:   Lenovo               HOSTNAME: DESKTOP-29PD6HE
lic: HOSTID: "00d49eac9d9b"       PID:      19280
lic: Using FLEXlm license file:
lic: 27000@DESKTOP-29PD6HE
lic: Checkout 1 hspice
lic: License/Maintenance for hspice will expire on 30-dec-2030/2030.12
lic: 1(in_use)/99(total) FLOATING license(s) on SERVER 27000@DESKTOP-29PD6HE
lic:

 init: begin read circuit files, cpu clock= 1.08E-01
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/behave/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/AD/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/XILINX/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/TLINE/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/TI/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/SIGNET/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/PCI/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/LIN_TEC
                         H/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/PET/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/DI0/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/COMLINE
                         /
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/BURR_BR
                         M/
       option search = C:/synopsys/Hspice_P-2019.06-SP1-1/parts/BJT/
       option post =     2.00
       option measfall =     0.00
       option ingold =     2.00
       option nomod
       option numdgt =     7.00
       option accurate
 init: end read circuit files, cpu clock= 1.14E-01 peak memory=      62 mb
 init: begin check errors, cpu clock= 1.14E-01
 init: end check errors, cpu clock= 1.21E-01 peak memory=      63 mb
 init: begin setup matrix, pivot=     0 cpu clock= 1.21E-01
       establish matrix -- done, cpu clock= 1.21E-01 peak memory=      63 mb
       re-order matrix -- done, cpu clock= 1.21E-01 peak memory=      63 mb
 init: end setup matrix, cpu clock= 1.22E-01 peak memory=      63 mb
 dcop: begin dcop, cpu clock= 1.22E-01
 dcop: end dcop, cpu clock= 1.47E-01 peak memory=      63 mb tot_iter=     197
 output: opamp.ma0
 sweep: ac ac0    begin, #sweeps= 181 cpu clock= 1.47E-01
 ac: frequency=1.00e+00
 ac: frequency=7.94e+00
 ac: frequency=6.31e+01
 ac: frequency=5.01e+02
 ac: frequency=3.98e+03
 ac: frequency=3.16e+04
 ac: frequency=2.51e+05
 ac: frequency=2.00e+06
 ac: frequency=1.58e+07
 ac: frequency=1.26e+08
 ac: frequency=1.00e+09
 sweep: ac ac0    end, cpu clock= 1.57E-01 peak memory=      63 mb
>info:         ***** hspice job concluded
 lic: Release hspice token(s)
