clc
clear 
close all
global pathsp moslib Lb Ub gmid t Vov1 u0 KS UGBS ydel
global L Cc Vdd Vss CL Co Av ts ovsht  Vinmax Vinmin vsd6 vds7 VTn Cox
global G0 beta a Ws Wsp Ls
global fs Ts pw1 pw2 fin1 T1 Tsim1 h amp1 

pathsp='C:\Synopsys\Hspice_P-2019.06-SP1-1\WIN64\hspice.com -mt 16 ';
kBOLTSMAN=1.381e-23; Temp=273+25;epislon=3.9*8.854e-12; 
un=275.5555875e-4; up=116.6094811e-4; 
toxn=4.1e-9; toxp=toxn;  %0.18um
UnCox=1e6*un*epislon/toxn; UpCox=1e6*up*epislon/toxp;
Cox=epislon/toxp;
VTn=0.485; VTp=-0.45; VTnmin=VTn; VTpmax=-VTp; % 0.18um  
moslib = '.lib ''rf018.l'' TT';
L=0.3; L1=0.3; L6=0.3; 
Cs=1.212; Ci=1.084; Co=1.492; CIN=0.12;  Ci3=0.0;
Ci=Ci+Ci3;
CL = Co + (Cs+CIN)*Ci/(Cs+CIN+Ci);
CL = Co + Cs*Ci/(Cs+Ci);
beta=Ci/(Cs+CIN+Ci); G0=Cs/Ci;
a=1+Ci*Ci/(Co*(Cs+Ci+CIN)+Ci*(Cs+CIN));
Ci=Ci-Ci3;
vdd=1.8;

voutx=0.4;Vov1=0.12; u0=voutx/G0;Cc=0.18*CL;%可调选项

Vdd=1.8; Vss=0;  Lb=0.18; Ub=100;  %180nm
Vout0=0.5*(Vdd+Vss);
Vin1=Vout0; Vin2=Vout0; vbs = 0;
load gm_id.txt -ascii;  gmid=gm_id;
% Performance Specifications
Tend=0.1e-6; h=0.01e-9; t=0:h:Tend;  
Vinmax=1.6; Vinmin=0.7; Av=70;   %0.18um



gm1 = 3.1921e-04;
UGB =1.1092e+03;
SR =133.1091;
K =1.0849;

kBOLTSMAN=1.381e-23; Temp=273+25;epislon=3.9*8.854e-12; 
un=3.4000000E-02; up=8.6610000E-03; 
toxn=3.87e-09; toxp=toxn;  %0.18um
UnCox=1e6*un*epislon/toxn; UpCox=1e6*up*epislon/toxp;
Cox=epislon/toxp;
VTn=0.5; VTp=-0.48; VTnmin=VTn; VTpmax=-VTp; % 0.18um  
Vout0=vdd/2;
Vov3=Vdd-Vinmax-VTpmax+VTnmin;
vsd22=Vdd-Vout0; vds24=Vout0-Vss;
icomp=2; 
Ibias=SR*Cc*1e-6; ids1=0.5*Ibias; ids22=SR*(Cc+CL)*1e-6;
gm1=UGB*Cc*1e-6;  gmid1 =gm1/ids1;

if (icomp==1)
    %gm22=2*pi*K*UGB*CL*1e-6;
    gm22=pi*K*UGB*CL*1e-6;
    if (gm22/ids22>16) ids22=gm22/16; end
else
         omegat22=K*UGB*(1+CL/Cc);   %Mega rad/s
    if (omegat22<4e3) 
        icomp=1;  gm22=2*pi*K*UGB*CL*1e-6; 
    else
        gmid22=interp1(gmid(:,4)./gmid(:,9),gmid(:,4),omegat22*1e6);
        gm22=gmid22*ids22;
    end
end


if (gm22/ids22>16) ids22=gm22/16; end
if isnan(gm22)
    gm22=2*pi*K*UGB*CL*1e-6;gmid22 = gm22/ids22;
end

[vgs22,W22]=inv_bsim1(-ids22,gm22,-vsd22,0,L6,1);

vsg24=vgs22; vsd3=-vgs22;
vgs23=vsg24;vds23=vgs23;
vds21=vdd-(-vgs23); vgs21=vgs22;

vsg3=0.7; vsd3=-vgs22;
W3=inv_bsim(-ids1,-vsg3,-vsd3,0,L,1);
[ids3,gm3,gds3]=eval_bsim(-vsg3,-vsd3,0,W3,L,1);

vgs1=(Vov1+VTn); Vs1=Vin1-vgs1; vds1=Vdd-vsd3-Vs1; vgs1a=1.1*vgs1;
while abs(vgs1/vgs1a-1)>0.5e-2
    [vgs1, W1]=inv_bsim1(Ibias/2,gm1,vds1,0,L1,-1);
    Vs1=Vin1-vgs1; vds1=Vdd-vsd3-Vs1; vgs1a=vgs1;
end
vds0=Vs1-Vss;
Vov0=Vinmin-Vss-vgs1; vgs0=Vov0+VTn;

W0=inv_bsim(Ibias,vgs0,vds0,0,L,-1);
[ids22,gm22,gds22]=eval_bsim(vgs22,-vsd22,0,W22,L6,1);ids22=-ids22;
if (icomp==1) Rc=1/gm22; else Rc=(1+CL/Cc)/gm22; end
W24=inv_bsim(ids22,-vsg24,vds24,0,L6,-1);
ids30=Ibias; %vds9=0.375;
W30=inv_bsim(ids30,vgs0,vds0,vbs,L,-1);


W31=inv_bsim(Ibias/2,vgs1,Vdd-vsg3-Vs1,vbs,L1,-1);
W33=inv_bsim(-Ibias/2,-vsg3,-vsg3,vbs,L,1);
W23=inv_bsim(ids22,-vgs23,-vgs23,vbs,L6,-1);
W21=inv_bsim(-ids22,vgs21,-vds21,vbs,L6,1);

W40=inv_bsim(Ibias,vgs0,vgs0,vbs,L,-1);


[pf]=eval_opamp(W0, W1, W3, W21, W22, W23, W24,  W30, W31, W33,W40,L,L1,L6,Cc,Ibias,Vdd,Vss,CL,Rc); 
[a]=eval_opampWRITE(W0, W1, W3, W21, W22, W23, W24, W30, W31, W33, W40,L,L1,L6,Cc,Ibias,Vdd,Vss,CL,Rc);
[pf2,tssp,ysp]=eval_int2(pw1,pw2,Ts,T1,Tsim1,Vdd,Vss,Cc,Rc,W0,W1,W3,W6,W7,W9,W10,W12,W14,Ws,Wsp,Ls,L,L1,L6,Ibias,Cs1,Cs2,Ci1,Ci2,Co);
disp(pf);
disp(K*180/pi);
disp(UGB);