*CMOS OPA
.OPTIONs post=2 measfall=0 ingold=2 NOMOD numdgt=7 accurate
.subckt ABOTA AVDD AVSS VCM VINNN VINNP VOUTN VOUTP
    M0 (net15 VB AVSS AVSS)      nch  w=  1.76601U L=  0.30000U
    M1 (net12 VINNP net15 net15) nch  w=  4.93294U L=  0.30000U
    M2 (net20 VINNN net15 net15) nch  w=  4.93294U L=  0.30000U
    M3 (net12 VBIAS AVDD AVDD)   pch  w=  3.15862U L=  0.30000U
    M4 (net20 VBIAS AVDD AVDD)   pch  w=  3.15862U L=  0.30000U
    M11 (net11 net20 AVDD AVDD)  pch  w=266.36190U L=  0.30000U
    M12 (VOUTP net12 AVDD AVDD)  pch  w=282.94412U L=  0.30000U
    M13 (net11 net11 AVSS AVSS)  nch  w= 65.68897U L=  0.30000U
    M14 (VOUTP net11 AVSS AVSS)  nch  w= 60.67801U L=  0.30000U
    M21 (net19 net12 AVDD AVDD)  pch  w=266.36190U L=  0.30000U
    M22 (VOUTN net20 AVDD AVDD)  pch  w=282.94412U L=  0.30000U
    M23 (net19 net19 AVSS AVSS)  nch  w= 65.68897U L=  0.30000U
    M24 (VOUTN net19 AVSS AVSS)  nch  w= 60.67801U L=  0.30000U
    M30 (net27 VB AVSS AVSS)      nch  w=  1.76601U L=  0.30000U
    M31 (VBIAS net24 net27 net27) nch  w=  5.19400U L=  0.30000U
    M32 (net038 VCM net27 net27)  nch  w=  5.19400U L=  0.30000U
    M33 (VBIAS VBIAS AVDD AVDD)   pch  w=  3.07445U L=  0.30000U
    M34 (net038 net038 AVDD AVDD)   pch  w=  3.07445U L=  0.30000U
    M40 (VB VB AVSS AVSS)         nch  w=  1.67375U L=  0.30000U
    R31 (VOUTN net24)    100K
    R32 (VOUTP net24)    100K
    C32 (VOUTP net24)    260f
    C31 (VOUTN net24)    260f
    C11 (net12 net50)    0.22706P
    C21 (net20 net51)    0.22706P
    R11 (net50 VOUTP)     1.50674K
    R21 (net51 VOUTN)     1.50674K
    IBIAS (AVDD VB)      41.64103U
.lib 'rf018.l' TT
.ENDS
VDD AVDD 0 DC   1.800
VSS AVSS 0 DC   0.000
CL1 VOUTN 0   2.064P
CL2 VOUTP 0   2.064P
X1 AVDD AVSS VCM VINN VINP VOUTN VOUTP ABOTA 
VINNd  101  0  0  AC 1 
EIN+  VINP VCM   101  0  1
EIN-  VCM  VINN  101  0  1
VCMA  VCM  0  DC  0.9 
.OP
.AC DEC 20 1 1000MEG
.PZ V(VOUTP) VINNd
.MEAS AC AD MAX VDB(VOUTP) FROM=1 TO=1000MEG
.MEAS AC UGB TRIG AT=1 TARG VDB(VOUTP) VAL=0 CROSS=1
.MEAS AC W3DB TRIG AT=1 TARG VDB(VOUTP) VAL='AD-3' CROSS=1
.MEAS AC PHASE FIND VP(VOUTP) WHEN VDB(VOUTP)=0
.MEAS AC PAMARGINE PARAM='180+PHASE' 
.MEAS AC POW AVG(POWER) FROM=1 TO=UGB
.END
