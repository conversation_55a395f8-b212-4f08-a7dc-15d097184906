%
% Evaluation of 2-stage opamp with conventional nulling resistor compensation
%
function [pf]=eval_opamp(W0, W1, W3, W21, W22, W23, W24, W30, W31, W33, W40,L,L1,L6,Cc,Ibias,Vdd,Vss,CL,Rc)    
    global  pathsp moslib ovsht t u0  h Tsim1 T1
    fp=fopen('opamp.sp','w+');
    fprintf(fp,'%s\n','*CMOS OPA');
    fprintf(fp,'%s\n','.OPTIONs post=2 measfall=0 ingold=2 NOMOD numdgt=7 accurate');  %
    fprintf(fp,'%s\n','.subckt ABOTA AVDD AVSS VCM VINNN VINNP VOUTN VOUTP');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M0 (net15 VB AVSS AVSS)      nch  w=',W0,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M1 (net12 VINNP net15 net15) nch  w=',W1,'U ' ,'L=',L1,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M2 (net20 VINNN net15 net15) nch  w=',W1,'U ' ,'L=',L1,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M3 (net12 VBIAS AVDD AVDD)   pch  w=',W3,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M4 (net20 VBIAS AVDD AVDD)   pch  w=',W3,'U ' ,'L=',L,'U');

    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M11 (net11 net20 AVDD AVDD)  pch  w=',W21,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M12 (VOUTP net12 AVDD AVDD)  pch  w=',W22,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M13 (net11 net11 AVSS AVSS)  nch  w=',W23,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M14 (VOUTP net11 AVSS AVSS)  nch  w=',W24,'U ' ,'L=',L6,'U');

    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M21 (net19 net12 AVDD AVDD)  pch  w=',W21,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M22 (VOUTN net20 AVDD AVDD)  pch  w=',W22,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M23 (net19 net19 AVSS AVSS)  nch  w=',W23,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M24 (VOUTN net19 AVSS AVSS)  nch  w=',W24,'U ' ,'L=',L6,'U');

    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M30 (net27 VB AVSS AVSS)      nch  w=',W30,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M31 (VBIAS net24 net27 net27) nch  w=',W31,'U ' ,'L=',L1,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M32 (net038 VCM net27 net27)  nch  w=',W31,'U ' ,'L=',L1,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M33 (VBIAS VBIAS AVDD AVDD)   pch  w=',W33,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M34 (net038 net038 AVDD AVDD)   pch  w=',W33,'U ' ,'L=',L,'U');

    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','    M40 (VB VB AVSS AVSS)         nch  w=',W40,'U ' ,'L=',L,'U');


    fprintf(fp,'%s\n','    R31 (VOUTN net24)    100K');
    fprintf(fp,'%s\n','    R32 (VOUTP net24)    100K');
    fprintf(fp,'%s\n','    C32 (VOUTP net24)    260f');
    fprintf(fp,'%s\n','    C31 (VOUTN net24)    260f');
    fprintf(fp,'%s%9.5f%s\n','    C11 (net12 net50)  ',Cc,'P');
    fprintf(fp,'%s%9.5f%s\n','    C21 (net20 net51)  ',Cc,'P');
    fprintf(fp,'%s%9.5f%s\n','    R11 (net50 VOUTP)   ',Rc/1e3,'K');
    fprintf(fp,'%s%9.5f%s\n','    R21 (net51 VOUTN)   ',Rc/1e3,'K');
    fprintf(fp,'%s%9.5f%s\n','    IBIAS (AVDD VB)     ',Ibias*1e6,'U');   
    fprintf(fp,'%s\n',moslib);
    fprintf(fp,'%s\n','.ENDS');
    fprintf(fp,'%s%7.3f\n','VDD AVDD 0 DC ', Vdd);
    fprintf(fp,'%s%7.3f\n','VSS AVSS 0 DC ', Vss);
    fprintf(fp,'%s%7.3f%s\n','CL1 VOUTN 0 ',CL,'P');
    fprintf(fp,'%s%7.3f%s\n','CL2 VOUTP 0 ',CL,'P');
    fprintf(fp,'%s\n','X1 AVDD AVSS VCM VINN VINP VOUTN VOUTP ABOTA ');
    fprintf(fp,'%s\n','VINNd  101  0  0  AC 1 ');
    fprintf(fp,'%s\n','EIN+  VINP VCM   101  0  1');
    fprintf(fp,'%s\n','EIN-  VCM  VINN  101  0  1');
    fprintf(fp,'%s\n','VCMA  VCM  0  DC  0.9 ');
%     fprintf(fp,'%s\n','VIN1 VINP  0   DC=0.9');
%     fprintf(fp,'%s\n','VIN2 VINN  0   DC=0.9');
    fprintf(fp,'%s\n','.OP');
    fprintf(fp,'%s\n','.AC DEC 20 1 1000MEG');
    fprintf(fp,'%s\n','.PZ V(VOUTP) VINNd');
    fprintf(fp,'%s\n','.MEAS AC AD MAX VDB(VOUTP) FROM=1 TO=1000MEG');
    fprintf(fp,'%s\n','.MEAS AC UGB TRIG AT=1 TARG VDB(VOUTP) VAL=0 CROSS=1');
    fprintf(fp,'%s\n','.MEAS AC W3DB TRIG AT=1 TARG VDB(VOUTP) VAL=''AD-3'' CROSS=1');
    fprintf(fp,'%s\n','.MEAS AC PHASE FIND VP(VOUTP) WHEN VDB(VOUTP)=0');
    fprintf(fp,'%s\n','.MEAS AC PAMARGINE PARAM=''180+PHASE'' ');
    fprintf(fp,'%s\n','.MEAS AC POW AVG(POWER) FROM=1 TO=UGB');
    % fprintf(fp,'%s\n','.ALTER');
    % pw=T1/2-0.2; 
    % fprintf(fp,'%s%10.3f%s%10.3f%s%10.3f%s\n','VINNd  101  0   pulse( 0',u0/2 ,' 0 0.1n 0.1n ', pw , 'n ' ,T1, 'n )');
    % 
    % 
    % 
    % 
    % fprintf(fp,'%s%10.4f%s%10.3f%s\n','.TRAN ', h/1e-9,'n' ,Tsim1,'u' );
    % fprintf(fp,'%s\n','.MEAS TRAN VMAX MAX V(VOUTN,VOUTP) FROM=50n TO=100n');
    % fprintf(fp,'%s\n','.MEAS TRAN VMIN MIN V(VOUTN,VOUTP) FROM=50n TO=100n');
    % fprintf(fp,'%s\n','.MEAS TRAN TRISE TRIG V(VOUTN,VOUTP) VAL=''VMIN+0.45*(VMAX-VMIN)'' RISE=1' );
    % fprintf(fp,'%s\n','+ TARG V(VOUTN,VOUTP) VAL=''VMIN+0.5*(VMAX-VMIN)'' RISE=1');
    % fprintf(fp,'%s\n','.MEAS SR PARAM=''(VMAX-VMIN)*0.05/TRISE'' ');
    % fprintf(fp,'%s\n','.print tran v(VOUTN,VOUTP)');
    fprintf(fp,'%s\n','.END');
    fclose(fp);
    hspcmd=sprintf('%s %s',pathsp,'-i opamp.sp -o opamp -b');
    system(hspcmd);
    %system('C:\\synopsys\\Hspice2004.09\\BIN\\hspice.exe -i opamp.sp -o opamp -b');     
	fp1=fopen('opamp.ma0','r+');
    for m=1:4
        tline=fgets(fp1);
    end
    ma=fscanf(fp1,'%lf',6);
    fclose(fp1);
	Av=ma(1);    
	UGB=ma(2)*1.0E-6;  
	f3db=ma(3)*1.0E-3; 
	if(ma(4)<0)
	   PM=ma(5);  
    else
	   PM=ma(4);
    end

    % fp1=fopen('opamp.mt1','r+');
	% for m=1:4
    %     tline=fgets(fp1);
    % end
    % mt=fscanf(fp1,'%lf',4);
    % fclose(fp1);
    % SR=mt(4)*1.0E-6;   



	Power=ma(6)*1.0E3; 
    pf(1)=Av; 
    pf(2)=UGB;
    pf(3)=PM;
    % pf(4)=SR;
    end

